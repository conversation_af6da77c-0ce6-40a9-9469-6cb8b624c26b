<!-- 桥隧病害处理 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>桥隧病害处理</span>
        <span class="header-subtitle">BRIDGE AND TUNNEL TREAT</span>
      </div>
    </template>
    <template #content>
      <div class="wh-full relative">
        <div class="controls-row">
          <div class="time-tabs">
            <span
              class="tab"
              :class="{ active: filterTime === 'day' }"
              @click="changeTimeFilter('day')"
            >日</span>
            <span
              class="tab"
              :class="{ active: filterTime === 'month' }"
              @click="changeTimeFilter('month')"
            >月</span>
            <span
              class="tab"
              :class="{ active: filterTime === 'year' }"
              @click="changeTimeFilter('year')"
            >年</span>
          </div>
          <div class="legend">
            <div class="legend-item">
              <span class="legend-color untreated"></span>
              <span class="legend-text">未处理</span>
            </div>
            <div class="legend-item">
              <span class="legend-color treated"></span>
              <span class="legend-text">已处理</span>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <CEcharts :option="option" />
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

// 属性 - 过滤时间
const filterTime = ref('day')

// 数据 - 配置项
const option = ref({})

// 假数据 - 不同时间维度的数据
const mockData = {
  day: [
    { name: '戴铁路大桥', untreated: 2, treated: 5 },
    { name: '安庄大桥', untreated: 1, treated: 3 },
    { name: '官井南隧道', untreated: 3, treated: 2 },
    { name: '新华大桥', untreated: 1, treated: 4 },
    { name: '青山隧道', untreated: 2, treated: 3 }
  ],
  month: [
    { name: '戴铁路大桥', untreated: 15, treated: 28 },
    { name: '安庄大桥', untreated: 8, treated: 22 },
    { name: '官井南隧道', untreated: 12, treated: 18 },
    { name: '新华大桥', untreated: 6, treated: 25 },
    { name: '青山隧道', untreated: 10, treated: 20 }
  ],
  year: [
    { name: '戴铁路大桥', untreated: 45, treated: 155 },
    { name: '安庄大桥', untreated: 32, treated: 128 },
    { name: '官井南隧道', untreated: 38, treated: 142 },
    { name: '新华大桥', untreated: 28, treated: 135 },
    { name: '青山隧道', untreated: 35, treated: 125 }
  ]
}

// 方法 - 切换时间过滤器
const changeTimeFilter = (timeType) => {
  filterTime.value = timeType
  updateChart()
}

// 方法 - 更新图表
const updateChart = () => {
  const currentData = mockData[filterTime.value]
  const maxValue = Math.max(...currentData.flatMap(item => [item.untreated + item.treated])) + 5

  option.value = {
    color: ['#00ffaf', '#306fff'],
    backgroundColor: 'transparent',
    grid: {
      top: 45,
      bottom: 30,
      left: '3%',
      right: '4%',
      containLabel: true
    },
    legend: {
      top: 5,
      right: 40,
      textStyle: {
        color: '#fff',
        rich: {
          a: {
            verticalAlign: 'middle'
          }
        },
        padding: [0, 0, -2, 0]
      }
    },
    xAxis: {
      type: 'category',
      data: currentData.map(item => item.name),
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        interval: 0,
        rotate: 0,
        margin: 15
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      max: maxValue,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '未处理',
        type: 'bar',
        barWidth: 10,
        stack: 'anyValue', // 堆叠标识，任何值，相同标识的会被堆叠在一起
        barMinHeight: 5,
        barGap: '50%',
        itemStyle: {
          borderRadius: [0, 0, 0, 0]
        },
        data: currentData.map(item => item.untreated)
      },
      {
        name: '已处理',
        type: 'bar',
        barWidth: 10,
        stack: 'anyValue',
        barMinHeight: 5,
        barGap: '50%',
        itemStyle: {
          borderRadius: [2, 2, 0, 0]
        },
        data: currentData.map(item => item.treated)
      }
    ]
  }
}

// 生命周期 - 挂载
onMounted(() => {
  updateChart()
})
</script>

<style lang="scss" scoped>
.wh-full {
  width: 100%;
  height: 100%;
}

.relative {
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: absolute;
  left: 20px;
  top: 0;
  z-index: 10;
  width: calc(100% - 40px);
}

.time-tabs {
  display: flex;
  gap: 0;

  .tab {
    padding: 4px 12px;
    font-size: 12px;
    color: #fff;
    border: 1px dashed rgb(0, 255, 175);
    cursor: pointer;
    background: transparent;
    transition: all 0.3s ease;
    box-shadow: none !important;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    &:not(:first-child) {
      border-left: none;
    }

    &.active {
      border-color: rgb(0, 255, 175) !important;
      background: rgba(2, 193, 201, 0.3) !important;
      color: #fff;
    }

    &:hover:not(.active) {
      background: rgba(0, 255, 175, 0.1);
    }
  }
}

.legend {
  display: flex;
  gap: 20px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;

      &.untreated {
        background: #00ffaf;
      }

      &.treated {
        background: #306fff;
      }
    }

    .legend-text {
      font-size: 12px;
      color: #fff;
    }
  }
}

.chart-container {
  width: 100%;
  height: 300px;
  padding-top: 45px;
}
</style>
